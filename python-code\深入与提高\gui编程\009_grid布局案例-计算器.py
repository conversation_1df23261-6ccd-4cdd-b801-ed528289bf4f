from tkinter import *

class Application(Frame):
    def __init__(self,master):
        super().__init__(master)
        self.master = master
        self.pack()
        self.createWidget()
    def createWidget(self):
        self.e1 = IntVar(self)
        self.entry01 = Entry(self,textvariable=self.e1,justify='right')
        self.entry01.grid(row=0, column=0, columnspan=4, sticky='nsew')
        self.e1.set(0)

        self.tumple = (
            ('MC','AC','*','÷'),
            ('7','8','9','+'),
            ('4','5','6','*'),
            ('1','2','3','='),
            ('0','.',)
        )
        for rindex, r in enumerate(self.tumple):
            for cindex, c in enumerate(r):
                self.btn = Button(self, text=c, fg='white', bg='black')
                self.btn.bind("<Button-1>", lambda event, arg=c: self.btnClick(arg))
                if c == '=':
                    self.btn.grid(row=rindex+1, column=cindex, rowspan=2, sticky='nsew')
                elif c == '.':
                    self.btn.grid(row=rindex+1, column=cindex, columnspan=2, sticky='nsew')
                else:
                    self.btn.grid(row=rindex+1, column=cindex, sticky='nsew')   
    def btnClick(self, c):
        print("按钮被点击了",c)

if __name__ == "__main__":
    root = Tk()
    root.title("计算器程序")    
    root.geometry("300x300+500+300")
    app = Application(root)
    app.mainloop()