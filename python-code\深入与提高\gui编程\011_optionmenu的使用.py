from tkinter import *

class Application(Frame):
    def __init__(self,master=None):
        super().__init__(master)
        self.master = master
        self.pack()
        self.createWidget()
    def createWidget(self):
        self.v1 = StringVar()
        self.v1.set("选项1")  # 设置默认值

        
        self.option_menu = OptionMenu(self, self.v1, "选项1", "选项2", "选项3")
        self.option_menu.pack()

        self.btn01 = Button(self, text="确定", command=self.confirm)
        self.btn01.pack()
    def confirm(self):
        print(self.v1.get())

if __name__ == "__main__":
    root = Tk()
    root.title("第一个GUI程序")
    root.geometry("500x500+500+300")
    app = Application(root)
    app.mainloop()