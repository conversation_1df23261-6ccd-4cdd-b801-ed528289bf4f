from tkinter import *

class Application(Frame):
    def __init__(self, master=None):
        super().__init__(master)
        self.master = master
        self.pack()
        self.create_menu()
        self.create_widgets()

    def create_menu(self):
        # 创建主菜单栏
        self.menu = Menu(self.master)
        
        self.file_menu = Menu(self.menu, tearoff=0)
        
        self.menu.add_cascade(label="文件", menu=self.file_menu)

        self.file_menu.add_command(label="新建",)
        self.file_menu.add_command(label="打开",)
        self.file_menu.add_command(label="保存", )
        self.file_menu.add_command(label="退出",)


        
        self.master.config(menu=self.menu)







    def create_widgets(self):
        pass

if __name__ == "__main__":
    root = Tk()
    root.title("第一个GUI程序")
    root.geometry("500x500+500+300")
    app = Application(root)
    app.mainloop()