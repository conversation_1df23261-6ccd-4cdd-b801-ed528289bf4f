from tkinter import *
class ContextMenuApp(Frame):
    def __init__(self, master):
        super().__init__(master)
        self.master = master
        self.pack(fill=BOTH, expand=True)
        self.create_widgets()
        self.create_context_menu()
      

    def create_widgets(self):
        # 创建文本区域
        self.text_area = Text(self, wrap=WORD)
        self.text_area.pack(fill=BOTH, expand=True, padx=5, pady=5)

        # 创建标签
        self.label = Label(self, text="右键点击我", bg="lightgray", height=3)
        self.label.pack(fill=X, padx=5, pady=5)

      
    def create_context_menu(self):
        # 创建文本区域的右键菜单
        self.text_context_menu = Menu(self.master, tearoff=0)
        self.text_context_menu.add_command(label="复制",)
        self.text_context_menu.add_command(label="粘贴",)
        self.text_context_menu.add_command(label="剪切", )
        self.text_context_menu.add_separator()
        self.text_context_menu.add_command(label="全选",)
        self.text_context_menu.add_separator()
        self.text_context_menu.add_command(label="清空",)

        # 创建标签的右键菜单
        self.label_context_menu = Menu(self.master, tearoff=0)
        self.label_context_menu.add_command(label="改变颜色",)
        self.label_context_menu.add_command(label="改变文本",)
        self.label_context_menu.add_separator()

        self.master.config(menu=self.text_context_menu)
if __name__ == "__main__":
    root = Tk()
    root.title("上下文菜单示例")
    app = ContextMenuApp(root)
    app.mainloop()