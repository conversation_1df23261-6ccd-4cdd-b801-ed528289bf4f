from tkinter import *
class ContextMenuApp(Frame):
    def __init__(self, master):
        super().__init__(master)
        self.master = master
        self.pack(fill=BOTH, expand=True)
        self.create_widgets()
        self.create_context_menu()
      

    def create_widgets(self):
        # 创建文本区域
        self.text_area = Text(self, wrap=WORD)
        self.text_area.pack(fill=BOTH, expand=True, padx=5, pady=5)

        # 创建标签
        self.label = Label(self, text="右键点击我", bg="lightgray", height=3)
        self.label.pack(fill=X, padx=5, pady=5)

      
    def create_context_menu(self):
        # 创建文本区域的右键菜单
        self.text_context_menu = Menu(self.master, tearoff=0)
        self.text_context_menu.add_command(label="复制", command=self.copy_text)
        self.text_context_menu.add_command(label="粘贴", command=self.paste_text)
        self.text_context_menu.add_command(label="剪切", command=self.cut_text)
        self.text_context_menu.add_separator()
        self.text_context_menu.add_command(label="全选", command=self.select_all)
        self.text_context_menu.add_separator()
        self.text_context_menu.add_command(label="清空", command=self.clear_text)

        # 创建标签的右键菜单
        self.label_context_menu = Menu(self.master, tearoff=0)
        self.label_context_menu.add_command(label="改变颜色", command=self.change_color)
        self.label_context_menu.add_command(label="改变文本", command=self.change_text)
        self.label_context_menu.add_separator()
        self.label_context_menu.add_command(label="关于", command=self.show_about)

        # 绑定右键点击事件
        self.text_area.bind("<Button-3>", self.show_text_context_menu)
        self.label.bind("<Button-3>", self.show_label_context_menu)

    def show_text_context_menu(self, event):
        """显示文本区域的右键菜单"""
        try:
            self.text_context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.text_context_menu.grab_release()

    def show_label_context_menu(self, event):
        """显示标签的右键菜单"""
        try:
            self.label_context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.label_context_menu.grab_release()

    # 文本区域菜单功能
    def copy_text(self):
        try:
            self.text_area.clipboard_clear()
            text = self.text_area.get("sel.first", "sel.last")
            self.text_area.clipboard_append(text)
        except:
            pass

    def paste_text(self):
        try:
            text = self.text_area.clipboard_get()
            self.text_area.insert("insert", text)
        except:
            pass

    def cut_text(self):
        try:
            text = self.text_area.get("sel.first", "sel.last")
            self.text_area.clipboard_clear()
            self.text_area.clipboard_append(text)
            self.text_area.delete("sel.first", "sel.last")
        except:
            pass

    def select_all(self):
        self.text_area.tag_add("sel", "1.0", "end")
        self.text_area.mark_set("insert", "1.0")
        self.text_area.see("insert")

    def clear_text(self):
        self.text_area.delete("1.0", "end")

    # 标签菜单功能
    def change_color(self):
        import random
        colors = ["lightblue", "lightgreen", "lightyellow", "lightpink", "lightgray"]
        color = random.choice(colors)
        self.label.config(bg=color)

    def change_text(self):
        import random
        texts = ["右键点击我", "Hello World!", "菜单测试", "颜色已改变", "Tkinter GUI"]
        text = random.choice(texts)
        self.label.config(text=text)

    def show_about(self):
        from tkinter import messagebox
        messagebox.showinfo("关于", "这是一个右键菜单示例程序")
if __name__ == "__main__":
    root = Tk()
    root.title("上下文菜单示例")
    app = ContextMenuApp(root)
    app.mainloop()