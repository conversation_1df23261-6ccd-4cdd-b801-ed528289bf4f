from tkinter import *
from tkinter import messagebox
class Application(Frame):
    def __init__(self,master=None):
        super().__init__(master)
        self.master = master
        self.pack()
        self.createWidget()


    def createWidget(self):
        self.label01 = Label(self, text="用户名")
        self.label01.pack()

        self.v1 = StringVar()
        self.ventry01 = Entry(self, textvariable=self.v1)
        self.ventry01.pack()

        self.btn01 = Button(self, text="登录", command=self.login)
        self.btn01.pack()
    
    def login(self):
        if self.v1.get() == "root":
            # messagebox.showinfo("成功", "登录成功！")
            # 先关闭登录窗口，再打开主窗口
            self.master.destroy()
            self.open_main()
        else:
            messagebox.showerror("错误", "用户名错误，请输入 'root'")

    def open_main(self):
        # 创建新的主窗口
        main_window = Tk()
        main_window.title("主窗口")
        main_window.geometry("400x300+600+400")

        self.radio_var = StringVar()
        self.radio_var.set("模式1")

        self.radio01 = Radiobutton(main_window, text="模式1", value="模式1", variable=self.radio_var)
        self.radio02 = Radiobutton(main_window, text="模式2", value="模式2", variable=self.radio_var)

        self.radio01.pack()
        self.radio02.pack()

        self.check_bk_var = IntVar()
        self.check_ft_var = IntVar()

        self.check_bk_var.set(1)
        self.check_ft_var.set(1)


        self.check_01 = Checkbutton(main_window, text="篮球", variable=self.check_bk_var , onvalue=1, offvalue=0)
        self.check_02 = Checkbutton(main_window, text="足球", variable=self.check_ft_var , onvalue=1, offvalue=0)
        self.check_01.pack()
        self.check_02.pack()

        self.btn02 = Button(main_window, text="确定", command=self.confirm)
        self.btn02.pack()
        # 启动主窗口的事件循环
        main_window.mainloop()

    def confirm(self):
        print(self.radio_var.get())
        print(self.check_bk_var.get())
        print(self.check_ft_var.get())

if __name__ == "__main__":
    root = Tk()
    root.title("第一个GUI程序")
    root.geometry("500x500+500+300")
    app = Application(root)
    app.mainloop()