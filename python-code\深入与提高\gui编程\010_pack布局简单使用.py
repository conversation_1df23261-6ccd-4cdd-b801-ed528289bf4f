from tkinter import *
class Application(Frame):
    def __init__(self,master=None):
        super().__init__(master)
        self.master = master
        self.pack()
        self.createWidget()
    def createWidget(self):
        for i in range(10):
            Button(self, bg= 'black' if i % 2 == 0 else 'white' ,height=20,width=5).pack(side=LEFT,)

if __name__ == "__main__":
    root = Tk()
    root.title("第一个GUI程序")
    root.geometry("500x500+500+300")
    app = Application(root)
    app.mainloop()